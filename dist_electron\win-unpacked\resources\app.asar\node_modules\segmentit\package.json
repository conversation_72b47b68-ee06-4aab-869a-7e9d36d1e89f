{"name": "segmentit", "main": "dist/cjs", "module": "dist/esm/segmentit.js", "browser": "dist/umd/segmentit.js", "files": ["dist"], "version": "2.0.3", "description": "Chinese word segmentation 中文分词模块 with browser && electron support", "homepage": "https://github.com/linonetwo/segmentit#readme", "authors": ["<PERSON><PERSON> <<EMAIL>>", "Linonetwo <<EMAIL>> (https://onetwo.ren/)"], "repository": {"type": "git", "url": "git://github.com/linonetwo/segmentit.git"}, "browserslist": "> 0.25%, not dead", "jest": {"testEnvironment": "node", "moduleFileExtensions": ["js"], "watchman": false, "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js"]}, "dependencies": {"preval.macro": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.7.7", "@babel/core": "^7.7.7", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.7", "@babel/preset-flow": "^7.7.4", "babel-eslint": "^10.0.3", "babel-jest": "^24.9.0", "babel-plugin-macros": "^2.8.0", "eslint": "^6.8.0", "eslint-config-airbnb": "^18.0.1", "eslint-plugin-compat": "^3.3.0", "eslint-plugin-flowtype": "~4.5.2", "eslint-plugin-import": "^2.19.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.17.0", "flow-bin": "^0.114.0", "flow-typed": "^2.6.2", "jest": "^24.9.0", "mocha": "^6.2.2", "rimraf": "^3.0.0", "rollup": "^1.27.13", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.4", "should": "^13.2.3", "uglify-es": "^3.3.9"}}