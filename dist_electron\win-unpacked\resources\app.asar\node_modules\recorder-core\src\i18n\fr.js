/*
Recorder i18n/fr.js
https://github.com/xiangyuecn/Recorder

Usage: Recorder.i18n.lang="fr"

Desc: French, Français, 法语。Cette traduction provient principalement de : traduction google + traduction Baidu, traduit du chinois vers le français. 此翻译主要来自：google翻译+百度翻译，由中文翻译成法语。

注意：请勿修改//@@打头的文本行；以下代码结构由/src/package-i18n.js自动生成，只允许在字符串中填写翻译后的文本，请勿改变代码结构；翻译的文本如果需要明确的空值，请填写"=Empty"；文本中的变量用{n}表示（n代表第几个变量），所有变量必须都出现至少一次，如果不要某变量用{n!}表示

Note: Do not modify the text lines starting with //@@; The following code structure is automatically generated by /src/package-i18n.js, only the translated text is allowed to be filled in the string, please do not change the code structure; If the translated text requires an explicit empty value, please fill in "=Empty"; Variables in the text are represented by {n} (n represents the number of variables), all variables must appear at least once, if a variable is not required, it is represented by {n!}
*/
(function(factory){
	var browser=typeof window=="object" && !!window.document;
	var win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面
	factory(win.Recorder,browser);
}(function(Recorder,isBrowser){
"use strict";
var i18n=Recorder.i18n;

//@@User Code-1 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-1 End @@

//@@Exec i18n.lang="fr";
Recorder.CLog('Import Recorder i18n lang="fr"');

//i18n.alias["other-lang-key"]="fr";

var putSet={lang:"fr"};

i18n.data["rtl$fr"]=false;
i18n.data["desc$fr"]="French, Français, 法语。Cette traduction provient principalement de : traduction google + traduction Baidu, traduit du chinois vers le français. 此翻译主要来自：google翻译+百度翻译，由中文翻译成法语。";
//@@Exec i18n.GenerateDisplayEnglish=true;



//*************** Begin srcFile=recorder-core.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="重复导入{1}"
//@@en="Duplicate import {1}"
//@@Put0
 "K8zP:"+ //args: {1}
       "Répéter l'importation {1}"

//@@zh="剩{1}个GetContext未close"
//@@en="There are {1} GetContext unclosed"
,"mSxV:"+ //args: {1}
       "Les {1} GetContext restants n'ont pas été close"

//@@zh="（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）"
//@@en=" (Note: ctx is not in the running state. At least one of rec.open and start must be called during user operations (touch, click, etc.), otherwise ctx.resume will be attempted during rec.start, which may cause compatibility issues (iOS only), please refer to the runningContext configuration in the documentation) "
,"nMIy:"+ //no args
       " (Remarque : ctx n'est pas dans l'état running. Au moins l'un des rec.open et start doit être appelé pendant l'opération de l'utilisateur (toucher, cliquer, etc.), sinon ctx.resume sera tenté pendant rec.start, ce qui peut entraîner une compatibilité. problèmes (iOS uniquement), voir la configuration de runningContext dans la documentation) "

//@@zh="Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，"
//@@en="The sampleRate of the Stream {1} is not equal to {2}, so the sampleRate conversion will be performed (note: the sound quality will not improve and may even deteriorate). This phenomenon mainly occurs when echoCancellation is not disabled on the mobile terminal. When the browser has echoCancellation, it may only return audio data with a sampleRate of 16k. "
,"eS8i:"+ //args: {1}-{2}
       "Si le sampleRate {1} du Stream n'est pas égal à {2}, la conversion sampleRate sera effectuée (attention : la qualité du son ne s'améliorera pas ou pourra même se dégrader. Ce phénomène se produit principalement lorsque echoCancellation n'est pas désactivé sur le mobile). terminal Lorsque le navigateur a echoCancellation, seules les données audio avec un taux d'échantillonnage de 16k seront renvoyées. "

//@@zh="。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。"
//@@en=". Due to 375 callbacks in 1 second in {1}, there may be performance problems on the mobile side, which may cause the callback to be lost and the recording to be shortened, but it will not affect the PC side. It is not recommended to enable {1} for now."
,"ZGlf:"+ //args: {1}
       ". En raison des 375 rappels par seconde dans un délai de {1}, il peut y avoir des problèmes de performances du côté mobile qui peuvent entraîner la perte des rappels et un raccourcissement de l'enregistrement. Il n'y a aucun impact du côté PC. Il n'est pas recommandé d'activer {1} pour le moment."

//@@zh="Connect采用老的{1}，"
//@@en="Connect uses the old {1}, "
,"7TU0:"+ //args: {1}
       "Connect utilise l'ancien {1}, "

//@@zh="但已设置{1}尝试启用{2}"
//@@en="But {1} is set trying to enable {2}"
,"JwCL:"+ //args: {1}-{2}
       "Mais {1} est configuré pour essayer d'activer {2}"

//@@zh="可设置{1}尝试启用{2}"
//@@en="Can set {1} try to enable {2}"
,"VGjB:"+ //args: {1}-{2}
       "Vous pouvez configurer {1} pour essayer d'activer {2}"

//@@zh="{1}未返回任何音频，恢复使用{2}"
//@@en="{1} did not return any audio, reverting to {2}"
,"MxX1:"+ //args: {1}-{2}
       "{1} n'a renvoyé aucun son et a repris l'utilisation de {2}"

//@@zh="{1}多余回调"
//@@en="{1} redundant callback"
,"XUap:"+ //args: {1}
       "{1} rappel redondant"

//@@zh="Connect采用{1}，设置{2}可恢复老式{3}"
//@@en="Connect uses {1}, set {2} to restore old-fashioned {3}"
,"yOta:"+ //args: {1}-{3}
       "Connect utilise {1} et la configuration de {2} peut restaurer l'ancien {3}"

//@@zh="（此浏览器不支持{1}）"
//@@en=" (This browser does not support {1}) "
,"VwPd:"+ //args: {1}
       " (Ce navigateur ne prend pas en charge {1}) "

//@@zh="{1}未返回任何音频，降级使用{2}"
//@@en="{1} did not return any audio, downgrade to {2}"
,"vHnb:"+ //args: {1}-{2}
       "{1} ne renvoie aucun audio, rétrogradez pour utiliser {2}"

//@@zh="{1}多余回调"
//@@en="{1} redundant callback"
,"O9P7:"+ //args: {1}
       "{1} rappel redondant"

//@@zh="Connect采用{1}，设置{2}可恢复使用{3}或老式{4}"
//@@en="Connect uses {1}, set {2} to restore to using {3} or old-fashioned {4}"
,"LMEm:"+ //args: {1}-{4}
       "Connect utilise {1}, la configuration de {2} peut restaurer l'utilisation de {3} ou de l'ancien {4}"

//@@zh="{1}的filter采样率变了，重设滤波"
//@@en="The filter sampleRate of {1} has changed, reset the filter"
,"d48C:"+ //args: {1}
       "Le taux d'échantillonnage du filtre de {1} a changé, réinitialisez le filtre"

//@@zh="{1}似乎传入了未重置chunk {2}"
//@@en="{1} seems to have passed in an unreset chunk {2}"
,"tlbC:"+ //args: {1}-{2}
       "{1} semble passer dans chunk {2} qui n'est pas réinitialisé"

//@@zh="{1}和{2}必须是数值"
//@@en="{1} and {2} must be number"
,"VtS4:"+ //args: {1}-{2}
       "{1} et {2} doivent être des valeurs numériques"

//@@zh="录音open失败："
//@@en="Recording open failed: "
,"5tWi:"+ //no args
       "L'enregistrement de open a échoué : "

//@@zh="open被取消"
//@@en="open cancelled"
,"dFm8:"+ //no args
       "open a été annulé"

//@@zh="open被中断"
//@@en="open interrupted"
,"VtJO:"+ //no args
       "open a été interrompu"

//@@zh="，可尝试使用RecordApp解决方案"
//@@en=", you can try to use the RecordApp solution "
,"EMJq:"+ //no args
       ", vous pouvez essayer la solution RecordApp"

//@@zh="不能录音："
//@@en="Cannot record: "
,"A5bm:"+ //no args
       "Impossible d'enregistrer : "

//@@zh="不支持此浏览器从流中获取录音"
//@@en="This browser does not support obtaining recordings from stream"
,"1iU7:"+ //no args
       "Ce navigateur ne prend pas en charge la récupération d'enregistrements à partir de flux"

//@@zh="从流中打开录音失败："
//@@en="Failed to open recording from stream: "
,"BTW2:"+ //no args
       "Échec de l'ouverture de l'enregistrement à partir du flux : "

//@@zh="无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})"
//@@en="No permission to record (cross domain, please try adding microphone access policy to iframe, such as: {1})"
,"Nclz:"+ //args: {1}
       "Aucune autorisation d'enregistrement (inter-domaine, veuillez essayer d'ajouter une stratégie d'accès au microphone à l'iframe, telle que {1})"

//@@zh="，无可用麦克风"
//@@en=", no microphone available"
,"jBa9:"+ //no args
       ", pas de micro disponible"

//@@zh="用户拒绝了录音权限"
//@@en="User denied recording permission"
,"gyO5:"+ //no args
       "L'utilisateur a refusé l'autorisation d'enregistrement"

//@@zh="浏览器禁止不安全页面录音，可开启https解决"
//@@en="Browser prohibits recording of unsafe pages, which can be resolved by enabling HTTPS"
,"oWNo:"+ //no args
       "Le navigateur interdit l'enregistrement des pages dangereuses, ce qui peut être résolu en activant https"

//@@zh="此浏览器不支持录音"
//@@en="This browser does not support recording"
,"COxc:"+ //no args
       "Ce navigateur ne prend pas en charge l'enregistrement"

//@@zh="发现同时多次调用open"
//@@en="It was found that open was called multiple times at the same time"
,"upb8:"+ //no args
       "J'ai constaté que open était appelé plusieurs fois en même temps"

//@@zh="录音功能无效：无音频流"
//@@en="Invalid recording: no audio stream"
,"Q1GA:"+ //no args
       "La fonction d'enregistrement ne fonctionne pas : pas de flux audio"

//@@zh="，将尝试禁用回声消除后重试"
//@@en=", will try to disable echoCancellation and try again"
,"KxE2:"+ //no args
       ", je vais essayer de désactiver echoCancellation et réessayer"

//@@zh="请求录音权限错误"
//@@en="Error requesting recording permission"
,"xEQR:"+ //no args
       "Erreur lors de la demande d'autorisation d'enregistrement"

//@@zh="无法录音："
//@@en="Unable to record: "
,"bDOG:"+ //no args
       "Impossible d'enregistrer : "

//@@zh="注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象"
//@@en="Note: The {1} parameter has been configured, which may cause the browser to not correctly select the microphone, or the mobile terminal to not enable echoCancellation, etc. "
,"IjL3:"+ //args: {1}
       "Remarque : Le paramètre {1} a été configuré, ce qui peut empêcher le navigateur de sélectionner correctement le microphone ou le terminal mobile de ne pas activer echoCancellation, etc. "

//@@zh="，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置"
//@@en=", when {1} is not configured, the browser may automatically enable echoCancellation. When echoCancellation is not disabled on the mobile terminal, the system playback volume may be reduced (can be restored after closing the recording) and only 16k sampleRate audio stream is provided (when echoCancellation is not required, it can be explicitly configured to disable to obtain 48k high-quality stream). Please refer to the {2} configuration in the document"
,"RiWe:"+ //args: {1}-{2}
       ", lorsque {1} n'est pas configuré, le navigateur peut activer automatiquement echoCancellation. Lorsque echoCancellation n'est pas désactivé sur le terminal mobile, le volume de lecture du système peut être réduit (peut être restauré après la fermeture de l'enregistrement) et seul le flux audio à 16 k de fréquence d'échantillonnage est fourni (lorsque echoCancellation n'est pas requis, il peut être explicitement configuré pour être désactivé afin d'obtenir un flux de haute qualité à 48 k). Veuillez vous référer à la configuration {2} dans le document"

//@@zh="close被忽略（因为同时open了多个rec，只有最后一个会真正close）"
//@@en="close is ignored (because multiple recs are open at the same time, only the last one will actually close)"
,"hWVz:"+ //no args
       "close est ignoré (car plusieurs recs sont ouverts en même temps, seul le dernier sera en fait close)"

//@@zh="忽略"
//@@en="ignore"
,"UHvm:"+ //no args
       "négligence"

//@@zh="不支持{1}架构"
//@@en="{1} architecture not supported"
,"Essp:"+ //args: {1}
       "Ne prend pas en charge l'architecture {1}"

//@@zh="{1}类型不支持设置takeoffEncodeChunk"
//@@en="{1} type does not support setting takeoffEncodeChunk"
,"2XBl:"+ //args: {1}
       "Le type {1} ne prend pas en charge le paramètre takeoffEncodeChunk"

//@@zh="(未加载编码器)"
//@@en="(Encoder not loaded)"
,"LG7e:"+ //no args
       "(aucun encodeur chargé)"

//@@zh="{1}环境不支持实时处理"
//@@en="{1} environment does not support real-time processing"
,"7uMV:"+ //args: {1}
       "L'environnement {1} ne prend pas en charge le traitement en temps réel"

//@@zh="补偿{1}ms"
//@@en="Compensation {1}ms"
,"4Kfd:"+ //args: {1}
       "Compensation {1} ms"

//@@zh="未补偿{1}ms"
//@@en="Uncompensated {1}ms"
,"bM5i:"+ //args: {1}
       "Non compensé {1} ms"

//@@zh="回调出错是不允许的，需保证不会抛异常"
//@@en="Callback error is not allowed, you need to ensure that no exception will be thrown"
,"gFUF:"+ //no args
       "Les erreurs dans les rappels ne sont pas autorisées et doivent être assurées qu'aucune exception n'est levée"

//@@zh="低性能，耗时{1}ms"
//@@en="Low performance, took {1}ms"
,"2ghS:"+ //args: {1}
       "Faible performance, prend {1} ms"

//@@zh="未进入异步前不能清除buffers"
//@@en="Buffers cannot be cleared before entering async"
,"ufqH:"+ //no args
       "Les buffers ne peuvent pas être effacés avant d'entrer en mode asynchrone"

//@@zh="start失败：未open"
//@@en="start failed: not open"
,"6WmN:"+ //no args
       "Échec de start: pas open"

//@@zh="start 开始录音，"
//@@en="start recording, "
,"kLDN:"+ //no args
       "start, démarre l'enregistrement, "

//@@zh="start被中断"
//@@en="start was interrupted"
,"Bp2y:"+ //no args
       "start a été interrompu"

//@@zh="，可能无法录音："
//@@en=", may fail to record: "
,"upkE:"+ //no args
       ", l'enregistrement peut ne pas être possible : "

//@@zh="stop 和start时差:"
//@@en="Stop and start time difference: "
,"Xq4s:"+ //no args
       "stop, décalage horaire avec start : "

//@@zh="补偿:"
//@@en="compensate: "
,"3CQP:"+ //no args
       "compenser: "

//@@zh="结束录音失败："
//@@en="Failed to stop recording: "
,"u8JG:"+ //no args
       "Échec de la fin de l'enregistrement : "

//@@zh="，请设置{1}"
//@@en=", please set {1}"
,"1skY:"+ //args: {1}
       ", veuillez définir {1}"

//@@zh="结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b"
//@@en="Stop recording, encoding takes {1}ms, audio duration {2}ms, file size {3}b"
,"Wv7l:"+ //args: {1}-{3}
       "Terminer l'enregistrement. L'encodage prend {1} ms. La durée audio est de {2} ms. La taille du fichier est de {3}b"

//@@zh="{1}编码器返回的不是{2}"
//@@en="{1} encoder returned not {2}"
,"Vkbd:"+ //args: {1}-{2}
       "L'encodeur {1} ne renvoie pas {2}"

//@@zh="启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"
//@@en="After enabling takeoffEncodeChunk, the length of the blob returned by stop is 0 and no audio data is provided"
,"QWnr:"+ //no args
       "Après avoir activé takeoffEncodeChunk, la longueur du blob renvoyée par stop est 0 et aucune donnée audio n'est fournie"

//@@zh="生成的{1}无效"
//@@en="Invalid generated {1}"
,"Sz2H:"+ //args: {1}
       "Le {1} généré n'est pas valide"

//@@zh="未开始录音"
//@@en="Recording not started"
,"wf9t:"+ //no args
       "L'enregistrement n'a pas démarré"

//@@zh="，开始录音前无用户交互导致AudioContext未运行"
//@@en=", No user interaction before starting recording, resulting in AudioContext not running"
,"Dl2c:"+ //no args
       ", il n'y a aucune interaction de l'utilisateur avant de démarrer l'enregistrement, ce qui empêche AudioContext de s'exécuter"

//@@zh="未采集到录音"
//@@en="Recording not captured"
,"Ltz3:"+ //no args
       "Aucun enregistrement n'a été collecté"

//@@zh="未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载"
//@@en="The {1} encoder is not loaded. Please try to find the {1} encoder in the src/engine directory of the {2} and load it"
,"xGuI:"+ //args: {1}-{2}
       "L'encodeur {1} n'est pas chargé. Veuillez essayer de trouver l'encodeur {1} dans src/engine de {2} et de le charger"

//@@zh="录音错误："
//@@en="Recording error: "
,"AxOH:"+ //no args
       "Erreur d'enregistrement : "

//@@zh="音频buffers被释放"
//@@en="Audio buffers are released"
,"xkKd:"+ //no args
       "Les tampons audio sont libérés"

//@@zh="采样:{1} 花:{2}ms"
//@@en="Sampled: {1}, took: {2}ms"
,"CxeT:"+ //args: {1}-{2}
       "Échantillon : {1} Fleur : {2}ms"

//@@zh="非浏览器环境，不支持{1}"
//@@en="Non-browser environment, does not support {1}"
,"NonBrowser-1:"+ //args: {1}
       "Environnement sans navigateur, ne prend pas en charge {1}"

//@@zh="参数错误：{1}"
//@@en="Illegal argument: {1}"
,"IllegalArgs-1:"+ //args: {1}
       "Erreur de paramètre : {1}"

//@@zh="调用{1}需要先导入{2}"
//@@en="Calling {1} needs to import {2} first"
,"NeedImport-2:"+ //args: {1}-{2}
       "Pour appeler {1}, vous devez d'abord importer {2}"

//@@zh="不支持：{1}"
//@@en="Not support: {1}"
,"NotSupport-1:"+ //args: {1}
       "Non pris en charge : {1}"

//@@zh="覆盖导入{1}"
//@@en="Override import {1}"
,"8HO5:"+ //args: {1}
       "Remplacer l'importation {1}"

]);
//*************** End srcFile=recorder-core.js ***************



//*************** Begin srcFile=engine/beta-amr.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="AMR-NB(NarrowBand)，采样率设置无效（只提供8000hz），比特率范围：{1}（默认12.2kbps），一帧20ms、{2}字节；浏览器一般不支持播放amr格式，可用Recorder.amr2wav()转码成wav播放"
//@@en="AMR-NB (NarrowBand), sampleRate setting is invalid (only 8000hz is provided), bitRate range: {1} (default 12.2kbps), one frame 20ms, {2} bytes; browsers generally do not support playing amr format, available Recorder.amr2wav() transcoding into wav playback"
//@@Put0
 "b2mN:"+ //args: {1}-{2}
       "AMR-NB (NarrowBand), le paramètre sampleRate n'est pas valide (seul 8000 Hz est fourni), plage bitRate : {1} (par défaut 12.2 kbps), une image 20 ms, {2} octets ; les navigateurs ne prennent généralement pas en charge la lecture du format amr, disponible Recorder.amr2wav() Transcoder en wav pour la lecture"

//@@zh="AMR Info: 和设置的不匹配{1}，已更新成{2}"
//@@en="AMR Info: does not match the set {1}, has been updated to {2}"
,"tQBv:"+ //args: {1}-{2}
       "AMR Info : ne correspond pas à l'ensemble {1}, a été mis à jour vers {2}"

//@@zh="数据采样率低于{1}"
//@@en="Data sampleRate lower than {1}"
,"q12D:"+ //args: {1}
       "Les données sampleRate sont inférieures à {1}"

//@@zh="当前浏览器版本太低，无法实时处理"
//@@en="The current browser version is too low to process in real time"
,"TxjV:"+ //no args
       "La version actuelle du navigateur est trop basse et ne peut pas être traitée en temps réel"

//@@zh="takeoffEncodeChunk接管AMR编码器输出的二进制数据，只有首次回调数据（首帧）包含AMR头；在合并成AMR文件时，如果没有把首帧数据包含进去，则必须在文件开头添加上AMR头：Recorder.AMR.AMR_HEADER（转成二进制），否则无法播放"
//@@en="takeoffEncodeChunk takes over the binary data output by the AMR encoder, and only the first callback data (the first frame) contains the AMR header; when merging into an AMR file, if the first frame data is not included, the AMR header must be added at the beginning of the file: Recorder.AMR.AMR_HEADER (converted to binary), otherwise it cannot be played"
,"Q7p7:"+ //no args
       "takeoffEncodeChunk prend en charge les données binaires sorties par l'encodeur AMR. Seules les premières données de rappel (première image) contiennent l'en-tête AMR ; lors de la fusion dans un fichier AMR, si les données de la première image ne sont pas incluses, l'en-tête AMR doit être ajouté à la début du fichier : Recorder.AMR.AMR_HEADER (converti en binaire), sinon il ne peut pas être lu"

//@@zh="当前环境不支持Web Worker，amr实时编码器运行在主线程中"
//@@en="The current environment does not support Web Worker, and the amr real-time encoder runs in the main thread"
,"6o9Z:"+ //no args
       "L'environnement actuel ne prend pas en charge Web Worker et l'encodeur en temps réel amr s'exécute dans le thread principal"

//@@zh="amr worker剩{1}个未stop"
//@@en="amr worker left {1} unstopped"
,"yYWs:"+ //args: {1}
       "amr worker reste {1} non stop"

//@@zh="amr编码器未start"
//@@en="amr encoder not started"
,"jOi8:"+ //no args
       "encodeur amr pas start"

]);
//*************** End srcFile=engine/beta-amr.js ***************



//*************** Begin srcFile=engine/beta-ogg.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="Ogg Vorbis，比特率取值16-100kbps，采样率取值无限制"
//@@en="Ogg Vorbis, bitRate 16-100kbps, sampleRate unlimited"
//@@Put0
 "O8Gn:"+ //no args
       "Ogg Vorbis, la valeur bitRate est de 16 à 100 kbps, la valeur sampleRate est illimitée"

//@@zh="当前浏览器版本太低，无法实时处理"
//@@en="The current browser version is too low to process in real time"
,"5si6:"+ //no args
       "La version actuelle du navigateur est trop basse et ne peut pas être traitée en temps réel"

//@@zh="takeoffEncodeChunk接管OggVorbis编码器输出的二进制数据，Ogg由数据页组成，一页包含多帧音频数据（含几秒的音频，一页数据无法单独解码和播放），此编码器每次输出都是完整的一页数据，因此实时性会比较低；在合并成完整ogg文件时，必须将输出的所有数据合并到一起，否则可能无法播放，不支持截取中间一部分单独解码和播放"
//@@en="takeoffEncodeChunk takes over the binary data output by the OggVorbis encoder. Ogg is composed of data pages. One page contains multiple frames of audio data (including a few seconds of audio, and one page of data cannot be decoded and played alone). This encoder outputs a complete page of data each time, so the real-time performance will be relatively low; when merging into a complete ogg file, all the output data must be merged together, otherwise it may not be able to play, and it does not support intercepting the middle part to decode and play separately"
,"R8yz:"+ //no args
       "takeoffEncodeChunk prend en charge les données binaires sorties par l'encodeur OggVorbis. Ogg est composé de pages de données. Une page contient plusieurs images de données audio (y compris plusieurs secondes d'audio. Une page de données ne peut pas être décodée et lue séparément). Chaque sortie de ce L'encodeur est complet. Une page de données, donc les performances en temps réel seront relativement faibles ; lors de la fusion dans un fichier ogg complet, toutes les données de sortie doivent être fusionnées ensemble, sinon elles risquent de ne pas être lues et ne sont pas prises en charge. interceptez la partie médiane, décodez-la et jouez-la séparément"

//@@zh="当前环境不支持Web Worker，OggVorbis实时编码器运行在主线程中"
//@@en="The current environment does not support Web Worker, and the OggVorbis real-time encoder runs in the main thread"
,"hB9D:"+ //no args
       "L'environnement actuel ne prend pas en charge les Web Workers et l'encodeur en temps réel OggVorbis s'exécute dans le thread principal."

//@@zh="ogg worker剩{1}个未stop"
//@@en="There are {1} unstopped ogg workers"
,"oTiy:"+ //args: {1}
       "ogg worker reste {1} non stop"

//@@zh="ogg编码器未start"
//@@en="ogg encoder not started"
,"dIpw:"+ //no args
       "encodeur ogg pas start"

]);
//*************** End srcFile=engine/beta-ogg.js ***************



//*************** Begin srcFile=engine/beta-webm.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="此浏览器不支持进行webm编码，未实现MediaRecorder"
//@@en="This browser does not support webm encoding, MediaRecorder is not implemented"
//@@Put0
 "L49q:"+ //no args
       "Ce navigateur ne prend pas en charge l'encodage Webm et MediaRecorder n'est pas implémenté"

//@@zh="只有比较新的浏览器支持，压缩率和mp3差不多。由于未找到对已有pcm数据进行快速编码的方法，只能按照类似边播放边收听形式把数据导入到MediaRecorder，有几秒就要等几秒。输出音频虽然可以通过比特率来控制文件大小，但音频文件中的比特率并非设定比特率，采样率由于是我们自己采样的，到这个编码器随他怎么搞"
//@@en="Only newer browsers support it, and the compression rate is similar to mp3. Since there is no way to quickly encode the existing pcm data, the data can only be imported into MediaRecorder in a similar manner while playing and listening, and it takes a few seconds to wait for a few seconds. Although the output audio can control the file size through the bitRate, the bitRate in the audio file is not the set bitRate. Since the sampleRate is sampled by ourselves, we can do whatever we want with this encoder."
,"tsTW:"+ //no args
       "Seuls les navigateurs les plus récents le prennent en charge et le taux de compression est similaire à celui du mp3. Puisqu'il n'existe aucun moyen d'encoder rapidement les données pcm existantes, les données ne peuvent être importées dans MediaRecorder que d'une manière similaire de lecture et d'écoute, et vous devez attendre quelques secondes. Bien que la taille du fichier audio de sortie puisse être contrôlée par le débit binaire, le débit binaire du fichier audio n'est pas le débit binaire défini. Puisque le taux d'échantillonnage est échantillonné par nous-mêmes, nous pouvons faire ce que nous voulons avec cet encodeur"

//@@zh="此浏览器不支持把录音转成webm格式"
//@@en="This browser does not support converting recordings to webm format"
,"aG4z:"+ //no args
       "Ce navigateur ne prend pas en charge la conversion des enregistrements au format Webm"

//@@zh="转码webm出错：{1}"
//@@en="Error encoding webm: {1}"
,"PIX0:"+ //args: {1}
       "Erreur de transcodage Webm : {1}"

]);
//*************** End srcFile=engine/beta-webm.js ***************



//*************** Begin srcFile=engine/g711x.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="{1}；{2}音频文件无法直接播放，可用Recorder.{2}2wav()转码成wav播放；采样率比特率设置无效，固定为8000hz采样率、16位，每个采样压缩成8位存储，音频文件大小为8000字节/秒；如需任意采样率支持，请使用Recorder.{2}_encode()方法"
//@@en="{1}; {2} audio files cannot be played directly, and can be transcoded into wav by Recorder.{2}2wav(); the sampleRate bitRate setting is invalid, fixed at 8000hz sampleRate, 16 bits, each sample is compressed into 8 bits for storage, and the audio file size is 8000 bytes/second; if you need any sampleRate support, please use Recorder.{2}_encode() Method"
//@@Put0
 "d8YX:"+ //args: {1}-{2}
       "{1} ; {2} Le fichier audio ne peut pas être lu directement. Vous pouvez utiliser Recorder.{2}2wav() pour le transcoder en wav pour la lecture ; le paramètre de débit binaire du taux d'échantillonnage n'est pas valide et est fixé à 8 000 hz. taux, 16 bits, et chaque échantillon est compressé. dans un stockage de 8 bits, la taille du fichier audio est de 8 000 octets/seconde ; si vous avez besoin de prise en charge pour un taux d'échantillonnage, veuillez utiliser la méthode Recorder.{2}_encode()"

//@@zh="数据采样率低于{1}"
//@@en="Data sampleRate lower than {1}"
,"29UK:"+ //args: {1}
       "Les données sampleRate sont inférieures à {1}"

//@@zh="{1}编码器未start"
//@@en="{1} encoder not started"
,"quVJ:"+ //args: {1}
       "encodeur {1} pas start"

]);
//*************** End srcFile=engine/g711x.js ***************



//*************** Begin srcFile=engine/mp3.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="采样率范围：{1}；比特率范围：{2}（不同比特率支持的采样率范围不同，小于32kbps时采样率需小于32000）"
//@@en="sampleRate range: {1}; bitRate range: {2} (the sampleRate range supported by different bitRate is different, when the bitRate is less than 32kbps, the sampleRate must be less than 32000)"
//@@Put0
 "Zm7L:"+ //args: {1}-{2}
       "Plage sampleRate : {1} ; plage bitRate : {2} (différents bitRate prennent en charge différentes plages sampleRate. Lorsqu'il est inférieur à 32 kbit/s, le sampleRate doit être inférieur à 32000)"

//@@zh="{1}不在mp3支持的取值范围：{2}"
//@@en="{1} is not in the value range supported by mp3: {2}"
,"eGB9:"+ //args: {1}-{2}
       "{1} n'est pas dans la plage de valeurs prise en charge par mp3 : {2}"

//@@zh="sampleRate已更新为{1}，因为{2}不在mp3支持的取值范围：{3}"
//@@en="sampleRate has been updated to {1}, because {2} is not in the value range supported by mp3: {3}"
,"zLTa:"+ //args: {1}-{3}
       "sampleRate a été mis à jour à {1} car {2} n'est pas dans la plage de valeurs prise en charge par mp3 : {3}"

//@@zh="当前浏览器版本太低，无法实时处理"
//@@en="The current browser version is too low to process in real time"
,"yhUs:"+ //no args
       "La version actuelle du navigateur est trop basse et ne peut pas être traitée en temps réel"

//@@zh="当前环境不支持Web Worker，mp3实时编码器运行在主线程中"
//@@en="The current environment does not support Web Worker, and the mp3 real-time encoder runs in the main thread"
,"k9PT:"+ //no args
       "L'environnement actuel ne prend pas en charge Web Worker et l'encodeur en temps réel mp3 s'exécute dans le thread principal"

//@@zh="mp3 worker剩{1}个未stop"
//@@en="There are {1} unstopped mp3 workers left"
,"fT6M:"+ //args: {1}
       "mp3 worker left {1} unstopped"

//@@zh="mp3编码器未start"
//@@en="mp3 encoder not started"
,"mPxH:"+ //no args
       "encodeur mp3 pas start"

//@@zh="和设置的不匹配{1}，已更新成{2}"
//@@en="Does not match the set {1}, has been updated to {2}"
,"uY9i:"+ //args: {1}-{2}
       "Ne correspond pas au paramètre {1}, a été mis à jour vers {2}"

//@@zh="Fix移除{1}帧"
//@@en="Fix remove {1} frame"
,"iMSm:"+ //args: {1}
       "Fix supprime {1} images"

//@@zh="移除帧数过多"
//@@en="Remove too many frames"
,"b9zm:"+ //no args
       "Supprimer trop de cadres"

]);
//*************** End srcFile=engine/mp3.js ***************



//*************** Begin srcFile=engine/pcm.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="pcm为未封装的原始音频数据，pcm音频文件无法直接播放，可用Recorder.pcm2wav()转码成wav播放；支持位数8位、16位（填在比特率里面），采样率取值无限制"
//@@en="pcm is unencapsulated original audio data, pcm audio files cannot be played directly, and can be transcoded into wav by Recorder.pcm2wav(); it supports 8-bit and 16-bit bits (fill in the bitRate), and the sampleRate is unlimited"
//@@Put0
 "fWsN:"+ //no args
       "pcm est une donnée audio originale non encapsulée. Les fichiers audio Pcm ne peuvent pas être lus directement. Recorder.pcm2wav() peut être utilisé pour transcoder en wav pour la lecture. Il prend en charge 8 et 16 chiffres (remplis dans bitRate) et la valeur de sampleRate est illimitée."

//@@zh="PCM Info: 不支持{1}位，已更新成{2}位"
//@@en="PCM Info: {1} bit is not supported, has been updated to {2} bit"
,"uMUJ:"+ //args: {1}-{2}
       "PCM Info: Le bit {1} n'est pas pris en charge et a été mis à jour vers le bit {2}"

//@@zh="pcm2wav必须提供sampleRate和bitRate"
//@@en="pcm2wav must provide sampleRate and bitRate"
,"KmRz:"+ //no args
       "pcm2wav doit fournir sampleRate et bitRate"

//@@zh="pcm编码器未start"
//@@en="pcm encoder not started"
,"sDkA:"+ //no args
       "encodeur pcm pas start"

]);
//*************** End srcFile=engine/pcm.js ***************



//*************** Begin srcFile=engine/wav.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）"
//@@en="Supports 8-bit and 16-bit bits (fill in the bitRate), and the sampleRate is unlimited; this encoder only adds a 44-byte wav header before the pcm data, and the encoded 16-bit wav file removes the beginning 44 bytes to get pcm (note: other wav encoders may not be 44 bytes)"
//@@Put0
 "gPSE:"+ //no args
       "Prend en charge les chiffres de 8 bits et 16 bits (remplis dans bitRate) et la valeur de sampleRate est illimitée ; cet encodeur ajoute uniquement un en-tête wav de 44 octets avant les données pcm, et le fichier wav codé de 16 bits supprime les 44 premiers bits. Octets pour obtenir pcm (remarque : les autres encodeurs wav peuvent ne pas faire 44 octets)"

//@@zh="WAV Info: 不支持{1}位，已更新成{2}位"
//@@en="WAV Info: {1} bit is not supported, has been updated to {2} bit"
,"wyw9:"+ //args: {1}-{2}
       "WAV Info: Le bit {1} n'est pas pris en charge et a été mis à jour vers le bit {2}"

]);
//*************** End srcFile=engine/wav.js ***************



//*************** Begin srcFile=extensions/buffer_stream.player.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="getAudioSrc方法已过时：请直接使用getMediaStream然后赋值给audio.srcObject，仅允许在不支持srcObject的浏览器中调用本方法赋值给audio.src以做兼容"
//@@en="The getAudioSrc method is obsolete: please use getMediaStream directly and then assign it to audio.srcObject, it is only allowed to call this method in browsers that do not support srcObject and assign it to audio.src for compatibility"
//@@Put0
 "0XYC:"+ //no args
       "La méthode getAudioSrc est obsolète : veuillez utiliser getMediaStream directement et l'attribuer à audio.srcObject. Cette méthode ne peut être appelée que dans les navigateurs qui ne prennent pas en charge srcObject et attribuée à audio.src pour des raisons de compatibilité."

//@@zh="start被stop终止"
//@@en="start is terminated by stop"
,"6DDt:"+ //no args
       "start est terminé par stop"

//@@zh="{1}多次start"
//@@en="{1} repeat start"
,"I4h4:"+ //args: {1}
       "Répétition {1} start"

//@@zh="浏览器不支持打开{1}"
//@@en="The browser does not support opening {1}"
,"P6Gs:"+ //args: {1}
       "Le navigateur ne prend pas en charge l'ouverture de {1}"

//@@zh="（注意：ctx不是running状态，start需要在用户操作(触摸、点击等)时进行调用，否则会尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）"
//@@en=" (Note: ctx is not in the running state, start needs to be called when the user operates (touch, click, etc.), otherwise it will try to perform ctx.resume, which may cause compatibility issues (only iOS), please refer to the runningContext configuration in the document) "
,"JwDm:"+ //no args
       "(Remarque : ctx n'est pas dans l'état running. start doit être appelé lorsque l'utilisateur opère (toucher, cliquer, etc.), sinon ctx.resume sera tenté, ce qui peut entraîner des problèmes de compatibilité (iOS uniquement). Veuillez vous référer au configuration runningContext dans le document)"

//@@zh="此浏览器的AudioBuffer实现不支持动态特性，采用兼容模式"
//@@en="The AudioBuffer implementation of this browser does not support dynamic features, use compatibility mode"
,"qx6X:"+ //no args
       "L'implémentation AudioBuffer de ce navigateur ne prend pas en charge les fonctionnalités dynamiques et utilise le mode de compatibilité"

//@@zh="环境检测超时"
//@@en="Environment detection timeout"
,"cdOx:"+ //no args
       "Expiration du délai de détection de l'environnement"

//@@zh="可能无法播放：{1}"
//@@en="Could not play: {1}"
,"S2Bu:"+ //args: {1}
       "Peut ne pas jouer: {1}"

//@@zh="input调用失败：非pcm[Int16,...]输入时，必须解码或者使用transform转换"
//@@en="input call failed: non-pcm[Int16,...] input must be decoded or converted using transform"
,"ZfGG:"+ //no args
       "L'appel input a échoué: non - PCM [int16,...] en entrée, il doit être décodé ou converti avec transform"

//@@zh="input调用失败：未提供sampleRate"
//@@en="input call failed: sampleRate not provided"
,"N4ke:"+ //no args
       "L'appel input a échoué: sampleRate n'a pas été fourni"

//@@zh="input调用失败：data的sampleRate={1}和之前的={2}不同"
//@@en="input call failed: sampleRate={1} of data is different from previous={2}"
,"IHZd:"+ //args: {1}-{2}
       "L'appel input a échoué: sampleRate={1} de Data est différent de ={2} précédent"

//@@zh="延迟过大，已丢弃{1}ms {2}"
//@@en="The delay is too large, {1}ms has been discarded, {2}"
,"L8sC:"+ //args: {1}-{2}
       "Le délai est trop important, {1}ms ont été ignorées, {2}"

//@@zh="{1}未调用start方法"
//@@en="{1} did not call the start method"
,"TZPq:"+ //args: {1}
       "{1} la méthode start n'est pas appelée"

//@@zh="浏览器不支持音频解码"
//@@en="Browser does not support audio decoding"
,"iCFC:"+ //no args
       "Le navigateur ne supporte pas le décodage audio"

//@@zh="音频解码数据必须是ArrayBuffer"
//@@en="Audio decoding data must be ArrayBuffer"
,"wE2k:"+ //no args
       "Les données de décodage audio doivent être ArrayBuffer"

//@@zh="音频解码失败：{1}"
//@@en="Audio decoding failed: {1}"
,"mOaT:"+ //args: {1}
       "Le décodage audio a échoué: {1}"

]);
//*************** End srcFile=extensions/buffer_stream.player.js ***************



//*************** Begin srcFile=extensions/create-audio.nmn2pcm.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="符号[{1}]无效：{2}"
//@@en="Invalid symbol [{1}]: {2}"
//@@Put0
 "3RBa:"+ //args: {1}-{2}
       "Le symbole [{1}] est invalide: {2}"

//@@zh="音符[{1}]无效：{2}"
//@@en="Invalid note [{1}]: {2}"
,"U212:"+ //args: {1}-{2}
       "Note [{1}] invalide: {2}"

//@@zh="多个音时必须对齐，相差{1}ms"
//@@en="Multiple tones must be aligned, with a difference of {1}ms"
,"7qAD:"+ //args: {1}
       "Doit être aligné lorsque plusieurs tonalités, différence {1}ms"

//@@zh="祝你生日快乐"
//@@en="Happy Birthday to You"
,"QGsW:"+ //no args
       "Happy Birthday to You"

//@@zh="致爱丽丝"
//@@en="For Elise"
,"emJR:"+ //no args
       "For Elise"

//@@zh="卡农-右手简谱"
//@@en="Canon - Right Hand Notation"
,"GsYy:"+ //no args
       "Canon - symbole de la main droite"

//@@zh="卡农"
//@@en="Canon"
,"bSFZ:"+ //no args
       "Canon"

]);
//*************** End srcFile=extensions/create-audio.nmn2pcm.js ***************



//*************** Begin srcFile=extensions/sonic.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="当前环境不支持Web Worker，不支持调用Sonic.Async"
//@@en="The current environment does not support Web Worker and does not support calling Sonic.Async"
//@@Put0
 "Ikdz:"+ //no args
       "Web Worker n'est pas supporté dans l'environnement actuel, appel Sonic.Async n'est pas supporté"

//@@zh="sonic worker剩{1}个未flush"
//@@en="There are {1} unflushed sonic workers left"
,"IC5Y:"+ //args: {1}
       "sonic worker reste {1} non flush"

]);
//*************** End srcFile=extensions/sonic.js ***************



//*************** Begin srcFile=app-support/app-native-support.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="{1}中的{2}方法未实现，请在{3}文件中或配置文件中实现此方法"
//@@en="The {2} method in {1} is not implemented, please implement this method in the {3} file or configuration file"
//@@Put0
 "WWoj:"+ //args: {1}-{3}
       "La méthode {2} dans {1} n'est pas implémentée, implémentez - la dans un fichier {3} ou dans un fichier de configuration"

//@@zh="未开始录音，但收到Native PCM数据"
//@@en="Recording does not start, but Native PCM data is received"
,"rCAM:"+ //no args
       "L'enregistrement n'a pas commencé, mais les données Native PCM ont été reçues"

//@@zh="检测到跨域iframe，NativeRecordReceivePCM无法注入到顶层，已监听postMessage转发兼容传输数据，请自行实现将top层接收到数据转发到本iframe（不限层），不然无法接收到录音数据"
//@@en="A cross-domain iframe is detected. NativeRecordReceivePCM cannot be injected into the top layer. It has listened to postMessage to be compatible with data transmission. Please implement it by yourself to forward the data received by the top layer to this iframe (no limit on layer), otherwise the recording data cannot be received."
,"t2OF:"+ //no args
       "Iframe Cross - Domain détecté, NativeRecordReceivePCM ne peut pas être injecté à la couche supérieure, a écouté postMessage pour transmettre des données de transfert compatibles, s'il vous plaît implémenter vous - même pour transmettre les données reçues à la couche supérieure à cette iframe (couche illimitée), sinon les données d'enregistrement ne peuvent pas être reçues"

//@@zh="未开始录音"
//@@en="Recording not started"
,"Z2y2:"+ //no args
       "L'enregistrement n'a pas commencé"

]);
//*************** End srcFile=app-support/app-native-support.js ***************



//*************** Begin srcFile=app-support/app.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="重复导入{1}"
//@@en="Duplicate import {1}"
//@@Put0
 "uXtA:"+ //args: {1}
       "Importation répétée {1}"

//@@zh="注意：因为并发调用了其他录音相关方法，当前 {1} 的调用结果已被丢弃且不会有回调"
//@@en="Note: Because other recording-related methods are called concurrently, the current call result of {1} has been discarded and there will be no callback"
,"kIBu:"+ //args: {1}
       "Remarque : Étant donné que d'autres méthodes liées à l'enregistrement sont appelées simultanément, le résultat de l'appel actuel de {1} a été ignoré et il n'y aura pas de rappel"

//@@zh="重复注册{1}"
//@@en="Duplicate registration {1}"
,"ha2K:"+ //args: {1}
       "Enregistrement répété {1}"

//@@zh="仅清理资源"
//@@en="Clean resources only"
,"wpTL:"+ //no args
       "Nettoyage des ressources uniquement"

//@@zh="未开始录音"
//@@en="Recording not started"
,"bpvP:"+ //no args
       "L'enregistrement n'a pas commencé"

//@@zh="当前环境不支持实时回调，无法进行{1}"
//@@en="The current environment does not support real-time callback and cannot be performed {1}"
,"fLJD:"+ //args: {1}
       "L'environnement actuel ne prend pas en charge le Callback en temps réel, impossible de faire {1}"

//@@zh="录音权限请求失败："
//@@en="Recording permission request failed: "
,"YnzX:"+ //no args
       "La demande d'autorisation d'enregistrement a échoué: "

//@@zh="需先调用{1}"
//@@en="Need to call {1} first"
,"nwKR:"+ //args: {1}
       "Appelez d'abord {1}"

//@@zh="当前不是浏览器环境，需引入针对此平台的支持文件（{1}），或调用{2}自行实现接入"
//@@en="This is not a browser environment. You need to import support files for this platform ({1}), or call {2} to implement the access yourself."
,"citA:"+ //args: {1}-{2}
       "Actuellement, ce n'est pas un environnement de navigateur, il est nécessaire d'introduire un fichier de support ({1}) pour cette plate - forme ou d'appeler {2} pour implémenter l'accès par vous - même"

//@@zh="开始录音失败："
//@@en="Failed to start recording: "
,"ecp9:"+ //no args
       "Le début de l'enregistrement échoue: "

//@@zh="不能录音："
//@@en="Cannot record: "
,"EKmS:"+ //no args
       "Ne peut pas enregistrer: "

//@@zh="已开始录音"
//@@en="Recording started"
,"k7Qo:"+ //no args
       "Enregistrement commencé"

//@@zh="结束录音失败："
//@@en="Failed to stop recording: "
,"Douz:"+ //no args
       "Fin de l'enregistrement échoué: "

//@@zh="和Start时差：{1}ms"
//@@en="Time difference from Start: {1}ms"
,"wqSH:"+ //args: {1}
       "Et le décalage horaire de départ: {1}ms"

//@@zh="结束录音 耗时{1}ms 音频时长{2}ms 文件大小{3}b {4}"
//@@en="Stop recording, takes {1}ms, audio duration {2}ms, file size {3}b, {4}"
,"g3VX:"+ //args: {1}-{4}
       "Fin de l'enregistrement, prend du temps {1}ms Durée audio {2}ms , taille du fichier {3}b , {4}"

]);
//*************** End srcFile=app-support/app.js ***************

//@@User Code-2 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-2 End @@

}));