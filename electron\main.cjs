/* eslint-disable no-undef */
const { app, globalShortcut, ipcMain, BrowserWindow, screen, session } = require('electron')
const { spawn } = require('child_process')
const path = require('path')
require('dotenv').config()
let win
let keyListenerProcess = null
// let isListening = false

const createWindow = async (x, y) => {
  win = new BrowserWindow({
    x,
    y,
    width: 1080,
    height: 1920,
    frame: false, // 无边框
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: true,
      contextIsolation: true,
      enableRemoteModule: true
    }
  })
  win.show() // 显示并聚焦于窗口
  win.setMenu(null) // 去除菜单
  win.maximize() // 最大化窗口
  // 在Electron的主进程中
  const isDev = process.env.ELECTRON_IS_DEV === 'true'
  if (isDev) {
    // 开发环境配置
    win.loadURL(process.env.VITE_DEV_SERVER_URL)

    win.webContents.openDevTools() // 打开开发者工具

    // 指定本地扩展文件路径（需要实际路径）

    try {
      await session.defaultSession.loadExtension(
        path.resolve(__dirname, '../../Vuejs-devtools-6.6.1')
      )
    } catch (err) {
      console.log('Unable to load Vue DevTools111: ', err)
    }
  } else {
    // 生产环境配置
    console.log('Loading file:', path.join(__dirname, '../dist/index.html'))
    // win.loadURL('http://localhost:5173/')
    win.loadFile(path.join(__dirname, '../dist/index.html'))
  }
}

// 启动按键监听器
function startKeyListener() {
  if (keyListenerProcess) {
    return
  }

  const scriptPath = path.join(__dirname, 'keyListener.ps1')
  keyListenerProcess = spawn(
    'powershell.exe',
    ['-ExecutionPolicy', 'Bypass', '-File', scriptPath],
    {
      stdio: ['pipe', 'pipe', 'pipe']
    }
  )

  keyListenerProcess.stdout.on('data', (data) => {
    const output = data.toString().trim()
    console.log('Key event:', output)

    if (output === 'KEY_DOWN:F2') {
      // isListening = true
      // 通知所有窗口开始语音监听
      BrowserWindow.getAllWindows().forEach((win) => {
        win.webContents.send('voice-listening', { action: 'start' })
      })
    } else if (output === 'KEY_UP:F2') {
      // isListening = false
      // 通知所有窗口停止语音监听
      BrowserWindow.getAllWindows().forEach((win) => {
        win.webContents.send('voice-listening', { action: 'stop' })
      })
    }
  })

  keyListenerProcess.stderr.on('data', (data) => {
    console.error('Key listener error:', data.toString())
  })

  keyListenerProcess.on('close', (code) => {
    console.log('Key listener process closed with code:', code)
    keyListenerProcess = null
  })
}

// 停止按键监听器
function stopKeyListener() {
  if (keyListenerProcess) {
    keyListenerProcess.kill()
    keyListenerProcess = null
  }
}

function registerGlobalShortcut() {
  const ret = globalShortcut.register('F4 ', () => {
    console.log('F4 pressed globally!')

    // 通知所有窗口
    BrowserWindow.getAllWindows().forEach((win) => {
      win.webContents.send('global-shortcut', 'F4')
    })
  })

  if (!ret) {
    console.error('F4 registration failed')
  }
  // 检查是否注册成功
  console.log('F4 registered:', globalShortcut.isRegistered('F4'))
}

app.whenReady().then(() => {
  createWindow(1920 + 50, -835 + 50)

  // 启动按键监听器
  startKeyListener()

  // 注册全局快捷键 F4
  registerGlobalShortcut()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

app.on('ready', () => {
  // 创建 Electron 窗口等其他操作
  globalShortcut.register('Alt+CommandOrControl+I', () => {
    BrowserWindow.getFocusedWindow().webContents.openDevTools()
  })

  // 监听获取屏幕id事件
  ipcMain.handle('get-screen-id', async () => {
    // 获取所有屏幕的信息
    const displays = screen.getAllDisplays()
    // 选择一个屏幕
    const externalDisplay = displays.find((display) => {
      // 你可以根据需要来选择特定的屏幕，例如使用外接屏幕
      return display.bounds.x !== 0 || display.bounds.y !== 0
    })
    return externalDisplay.id
  })
})

// 窗口关闭时注销快捷键
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    globalShortcut.unregisterAll()
    stopKeyListener()
    app.quit()
  }
})

// 应用退出时注销
app.on('will-quit', () => {
  globalShortcut.unregisterAll()
  stopKeyListener()
})
