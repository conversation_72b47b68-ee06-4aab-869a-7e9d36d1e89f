{"version": 3, "file": "PixiPlugin.min.js", "sources": ["../src/PixiPlugin.js"], "sourcesContent": ["/*!\n * PixiPlugin 3.12.7\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license or for\n * Club GSAP members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _splitColor, _coreInitted, _PIXI, PropTween, _getSetter, _isV4, _isV8Plus,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isFunction = value => typeof(value) === \"function\",\n\t_warn = message => console.warn(message),\n\t_idMatrix = [1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],\n\t_lumR = 0.212671,\n\t_lumG = 0.715160,\n\t_lumB = 0.072169,\n\t_filterClass = name => _isFunction(_PIXI[name]) ? _PIXI[name] : _PIXI.filters[name], // in PIXI 7.1, filters moved from PIXI.filters to just PIXI\n\t_applyMatrix = (m, m2) => {\n\t\tlet temp = [],\n\t\t\ti = 0,\n\t\t\tz = 0,\n\t\t\ty, x;\n\t\tfor (y = 0; y < 4; y++) {\n\t\t\tfor (x = 0; x < 5; x++) {\n\t\t\t\tz = (x === 4) ? m[i + 4] : 0;\n\t\t\t\ttemp[i + x] = m[i]   * m2[x] + m[i+1] * m2[x + 5] +\tm[i+2] * m2[x + 10] + m[i+3] * m2[x + 15] +\tz;\n\t\t\t}\n\t\t\ti += 5;\n\t\t}\n\t\treturn temp;\n\t},\n\t_setSaturation = (m, n) => {\n\t\tlet inv = 1 - n,\n\t\t\tr = inv * _lumR,\n\t\t\tg = inv * _lumG,\n\t\t\tb = inv * _lumB;\n\t\treturn _applyMatrix([r + n, g, b, 0, 0, r, g + n, b, 0, 0, r, g, b + n, 0, 0, 0, 0, 0, 1, 0], m);\n\t},\n\t_colorize = (m, color, amount) => {\n\t\tlet c = _splitColor(color),\n\t\t\tr = c[0] / 255,\n\t\t\tg = c[1] / 255,\n\t\t\tb = c[2] / 255,\n\t\t\tinv = 1 - amount;\n\t\treturn _applyMatrix([inv + amount * r * _lumR, amount * r * _lumG, amount * r * _lumB, 0, 0, amount * g * _lumR, inv + amount * g * _lumG, amount * g * _lumB, 0, 0, amount * b * _lumR, amount * b * _lumG, inv + amount * b * _lumB, 0, 0, 0, 0, 0, 1, 0], m);\n\t},\n\t_setHue = (m, n) => {\n\t\tn *= Math.PI / 180;\n\t\tlet c = Math.cos(n),\n\t\t\ts = Math.sin(n);\n\t\treturn _applyMatrix([(_lumR + (c * (1 - _lumR))) + (s * (-_lumR)), (_lumG + (c * (-_lumG))) + (s * (-_lumG)), (_lumB + (c * (-_lumB))) + (s * (1 - _lumB)), 0, 0, (_lumR + (c * (-_lumR))) + (s * 0.143), (_lumG + (c * (1 - _lumG))) + (s * 0.14), (_lumB + (c * (-_lumB))) + (s * -0.283), 0, 0, (_lumR + (c * (-_lumR))) + (s * (-(1 - _lumR))), (_lumG + (c * (-_lumG))) + (s * _lumG), (_lumB + (c * (1 - _lumB))) + (s * _lumB), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1], m);\n\t},\n\t_setContrast = (m, n) => _applyMatrix([n,0,0,0,0.5 * (1 - n), 0,n,0,0,0.5 * (1 - n), 0,0,n,0,0.5 * (1 - n), 0,0,0,1,0], m),\n\t_getFilter = (target, type) => {\n\t\tlet filterClass = _filterClass(type),\n\t\t\tfilters = target.filters || [],\n\t\t\ti = filters.length,\n\t\t\tfilter;\n\t\tfilterClass || _warn(type + \" not found. PixiPlugin.registerPIXI(PIXI)\");\n\t\twhile (--i > -1) {\n\t\t\tif (filters[i] instanceof filterClass) {\n\t\t\t\treturn filters[i];\n\t\t\t}\n\t\t}\n\t\tfilter = new filterClass();\n\t\tif (type === \"BlurFilter\") {\n\t\t\tfilter.blur = 0;\n\t\t}\n\t\tfilters.push(filter);\n\t\ttarget.filters = filters;\n\t\treturn filter;\n\t},\n\t_addColorMatrixFilterCacheTween = (p, plugin, cache, vars) => { //we cache the ColorMatrixFilter components in a _gsColorMatrixFilter object attached to the target object so that it's easy to grab the current value at any time.\n\t\tplugin.add(cache, p, cache[p], vars[p]);\n\t\tplugin._props.push(p);\n\t},\n\t_applyBrightnessToMatrix = (brightness, matrix) => {\n\t\tlet filterClass = _filterClass(\"ColorMatrixFilter\"),\n\t\t\ttemp = new filterClass();\n\t\ttemp.matrix = matrix;\n\t\ttemp.brightness(brightness, true);\n\t\treturn temp.matrix;\n\t},\n\t_copy = obj => {\n\t\tlet copy = {},\n\t\t\tp;\n\t\tfor (p in obj) {\n\t\t\tcopy[p] = obj[p];\n\t\t}\n\t\treturn copy;\n\t},\n\t_CMFdefaults = {contrast:1, saturation:1, colorizeAmount:0, colorize:\"rgb(255,255,255)\", hue:0, brightness:1},\n\t_parseColorMatrixFilter = (target, v, pg) => {\n\t\tlet filter = _getFilter(target, \"ColorMatrixFilter\"),\n\t\t\tcache = target._gsColorMatrixFilter = target._gsColorMatrixFilter || _copy(_CMFdefaults),\n\t\t\tcombine = v.combineCMF && !(\"colorMatrixFilter\" in v && !v.colorMatrixFilter),\n\t\t\ti, matrix, startMatrix;\n\t\tstartMatrix = filter.matrix;\n\t\tif (v.resolution) {\n\t\t\tfilter.resolution = v.resolution;\n\t\t}\n\t\tif (v.matrix && v.matrix.length === startMatrix.length) {\n\t\t\tmatrix = v.matrix;\n\t\t\tif (cache.contrast !== 1) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"contrast\", pg, cache, _CMFdefaults);\n\t\t\t}\n\t\t\tif (cache.hue) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"hue\", pg, cache, _CMFdefaults);\n\t\t\t}\n\t\t\tif (cache.brightness !== 1) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"brightness\", pg, cache, _CMFdefaults);\n\t\t\t}\n\t\t\tif (cache.colorizeAmount) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"colorize\", pg, cache, _CMFdefaults);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"colorizeAmount\", pg, cache, _CMFdefaults);\n\t\t\t}\n\t\t\tif (cache.saturation !== 1) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"saturation\", pg, cache, _CMFdefaults);\n\t\t\t}\n\n\t\t} else {\n\t\t\tmatrix = _idMatrix.slice();\n\t\t\tif (v.contrast != null) {\n\t\t\t\tmatrix = _setContrast(matrix, +v.contrast);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"contrast\", pg, cache, v);\n\t\t\t} else if (cache.contrast !== 1) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _setContrast(matrix, cache.contrast);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"contrast\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (v.hue != null) {\n\t\t\t\tmatrix = _setHue(matrix, +v.hue);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"hue\", pg, cache, v);\n\t\t\t} else if (cache.hue) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _setHue(matrix, cache.hue);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"hue\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (v.brightness != null) {\n\t\t\t\tmatrix = _applyBrightnessToMatrix(+v.brightness, matrix);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"brightness\", pg, cache, v);\n\t\t\t} else if (cache.brightness !== 1) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _applyBrightnessToMatrix(cache.brightness, matrix);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"brightness\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (v.colorize != null) {\n\t\t\t\tv.colorizeAmount = (\"colorizeAmount\" in v) ? +v.colorizeAmount : 1;\n\t\t\t\tmatrix = _colorize(matrix, v.colorize, v.colorizeAmount);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"colorize\", pg, cache, v);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"colorizeAmount\", pg, cache, v);\n\t\t\t} else if (cache.colorizeAmount) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _colorize(matrix, cache.colorize, cache.colorizeAmount);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"colorize\", pg, cache, _CMFdefaults);\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"colorizeAmount\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (v.saturation != null) {\n\t\t\t\tmatrix = _setSaturation(matrix, +v.saturation);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"saturation\", pg, cache, v);\n\t\t\t} else if (cache.saturation !== 1) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _setSaturation(matrix, cache.saturation);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"saturation\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\ti = matrix.length;\n\t\twhile (--i > -1) {\n\t\t\tif (matrix[i] !== startMatrix[i]) {\n\t\t\t\tpg.add(startMatrix, i, startMatrix[i], matrix[i], \"colorMatrixFilter\");\n\t\t\t}\n\t\t}\n\t\tpg._props.push(\"colorMatrixFilter\");\n\t},\n\t_renderColor = (ratio, {t, p, color, set}) => {\n\t\tset(t, p, color[0] << 16 | color[1] << 8 | color[2]);\n\t},\n\t_renderDirtyCache = (ratio, {g}) => {\n\t\tif (_isV8Plus) {\n\t\t\tg.fill();\n\t\t\tg.stroke();\n\t\t} else if (g) { // in order for PixiJS to actually redraw GraphicsData, we've gotta increment the \"dirty\" and \"clearDirty\" values. If we don't do this, the values will be tween properly, but not rendered.\n\t\t\tg.dirty++;\n\t\t\tg.clearDirty++;\n\t\t}\n\t},\n\t_renderAutoAlpha = (ratio, data) => {\n\t\tdata.t.visible = !!data.t.alpha;\n\t},\n\t_addColorTween = (target, p, value, plugin) => {\n\t\tlet currentValue = target[p],\n\t\t\tstartColor = _splitColor(_isFunction(currentValue) ? target[ ((p.indexOf(\"set\") || !_isFunction(target[\"get\" + p.substr(3)])) ? p : \"get\" + p.substr(3)) ]() : currentValue),\n\t\t\tendColor = _splitColor(value);\n\t\tplugin._pt = new PropTween(plugin._pt, target, p, 0, 0, _renderColor, {t:target, p:p, color:startColor, set:_getSetter(target, p)});\n\t\tplugin.add(startColor, 0, startColor[0], endColor[0]);\n\t\tplugin.add(startColor, 1, startColor[1], endColor[1]);\n\t\tplugin.add(startColor, 2, startColor[2], endColor[2]);\n\t},\n\n\t_colorProps = {tint:1, lineColor:1, fillColor:1, strokeColor:1},\n\t_xyContexts = \"position,scale,skew,pivot,anchor,tilePosition,tileScale\".split(\",\"),\n\t_contexts = {x:\"position\", y:\"position\", tileX:\"tilePosition\", tileY:\"tilePosition\"},\n\t_colorMatrixFilterProps = {colorMatrixFilter:1, saturation:1, contrast:1, hue:1, colorize:1, colorizeAmount:1, brightness:1, combineCMF:1},\n\t_DEG2RAD = Math.PI / 180,\n\t_isString = value => typeof(value) === \"string\",\n\t_degreesToRadians = value => (_isString(value) && value.charAt(1) === \"=\") ? value.substr(0, 2) + (parseFloat(value.substr(2)) * _DEG2RAD) : value * _DEG2RAD,\n\t_renderPropWithEnd = (ratio, data) => data.set(data.t, data.p, ratio === 1 ? data.e : (Math.round((data.s + data.c * ratio) * 100000) / 100000), data),\n\t_addRotationalPropTween = (plugin, target, property, startNum, endValue, radians) => {\n\t\tlet cap = 360 * (radians ? _DEG2RAD : 1),\n\t\t\tisString = _isString(endValue),\n\t\t\trelative = (isString && endValue.charAt(1) === \"=\") ? +(endValue.charAt(0) + \"1\") : 0,\n\t\t\tendNum = parseFloat(relative ? endValue.substr(2) : endValue) * (radians ? _DEG2RAD : 1),\n\t\t\tchange = relative ? endNum * relative : endNum - startNum,\n\t\t\tfinalValue = startNum + change,\n\t\t\tdirection, pt;\n\t\tif (isString) {\n\t\t\tdirection = endValue.split(\"_\")[1];\n\t\t\tif (direction === \"short\") {\n\t\t\t\tchange %= cap;\n\t\t\t\tif (change !== change % (cap / 2)) {\n\t\t\t\t\tchange += (change < 0) ? cap : -cap;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (direction === \"cw\" && change < 0) {\n\t\t\t\tchange = ((change + cap * 1e10) % cap) - ~~(change / cap) * cap;\n\t\t\t} else if (direction === \"ccw\" && change > 0) {\n\t\t\t\tchange = ((change - cap * 1e10) % cap) - ~~(change / cap) * cap;\n\t\t\t}\n\t\t}\n\t\tplugin._pt = pt = new PropTween(plugin._pt, target, property, startNum, change, _renderPropWithEnd);\n\t\tpt.e = finalValue;\n\t\treturn pt;\n\t},\n\t_initCore = () => {\n\t\tif (!_coreInitted) {\n\t\t\tgsap = _getGSAP();\n\t\t\t_PIXI = _coreInitted = _PIXI || (_windowExists() && window.PIXI);\n\t\t\tlet version = (_PIXI && _PIXI.VERSION && parseFloat(_PIXI.VERSION.split(\".\")[0])) || 0;\n\t\t\t_isV4 = version === 4;\n\t\t\t_isV8Plus = version >= 8;\n\t\t\t_splitColor = color => gsap.utils.splitColor((color + \"\").substr(0,2) === \"0x\" ? \"#\" + color.substr(2) : color); // some colors in PIXI are reported as \"0xFF4421\" instead of \"#FF4421\".\n\t\t}\n\t}, i, p;\n\n//context setup...\nfor (i = 0; i < _xyContexts.length; i++) {\n\tp = _xyContexts[i];\n\t_contexts[p + \"X\"] = p;\n\t_contexts[p + \"Y\"] = p;\n}\n\n\nexport const PixiPlugin = {\n\tversion: \"3.12.7\",\n\tname: \"pixi\",\n\tregister(core, Plugin, propTween) {\n\t\tgsap = core;\n\t\tPropTween = propTween;\n\t\t_getSetter = Plugin.getSetter;\n\t\t_initCore();\n\t},\n\theadless: true, // doesn't need window\n\tregisterPIXI(pixi) {\n\t\t_PIXI = pixi;\n\t},\n\tinit(target, values, tween, index, targets) {\n\t\t_PIXI || _initCore();\n\t\tif (!_PIXI) {\n\t\t\t_warn(\"PIXI was not found. PixiPlugin.registerPIXI(PIXI);\");\n\t\t\treturn false;\n\t\t}\n\t\tlet context, axis, value, colorMatrix, filter, p, padding, i, data, subProp;\n\t\tfor (p in values) {\n\t\t\tcontext = _contexts[p];\n\t\t\tvalue = values[p];\n\t\t\tif (context) {\n\t\t\t\taxis = ~p.charAt(p.length-1).toLowerCase().indexOf(\"x\") ? \"x\" : \"y\";\n\t\t\t\tthis.add(target[context], axis, target[context][axis], (context === \"skew\") ? _degreesToRadians(value) : value, 0, 0, 0, 0, 0, 1);\n\t\t\t} else if (p === \"scale\" || p === \"anchor\" || p === \"pivot\" || p === \"tileScale\") {\n\t\t\t\tthis.add(target[p], \"x\", target[p].x, value);\n\t\t\t\tthis.add(target[p], \"y\", target[p].y, value);\n\t\t\t} else if (p === \"rotation\" || p === \"angle\") { //PIXI expects rotation in radians, but as a convenience we let folks define it in degrees and we do the conversion.\n\t\t\t\t_addRotationalPropTween(this, target, p, target[p], value, p === \"rotation\");\n\t\t\t} else if (_colorMatrixFilterProps[p]) {\n\t\t\t\tif (!colorMatrix) {\n\t\t\t\t\t_parseColorMatrixFilter(target, values.colorMatrixFilter || values, this);\n\t\t\t\t\tcolorMatrix = true;\n\t\t\t\t}\n\t\t\t} else if (p === \"blur\" || p === \"blurX\" || p === \"blurY\" || p === \"blurPadding\") {\n\t\t\t\tfilter = _getFilter(target, \"BlurFilter\");\n\t\t\t\tthis.add(filter, p, filter[p], value);\n\t\t\t\tif (values.blurPadding !== 0) {\n\t\t\t\t\tpadding = values.blurPadding || Math.max(filter[p], value) * 2;\n\t\t\t\t\ti = target.filters.length;\n\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\ttarget.filters[i].padding = Math.max(target.filters[i].padding, padding); //if we don't expand the padding on all the filters, it can look clipped.\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (_colorProps[p]) {\n\t\t\t\tif ((p === \"lineColor\" || p === \"fillColor\" || p === \"strokeColor\") && target instanceof _PIXI.Graphics) {\n\t\t\t\t\tdata = \"fillStyle\" in target ? [target] : (target.geometry || target).graphicsData; //\"geometry\" was introduced in PIXI version 5\n\t\t\t\t\tsubProp = p.substr(0, p.length - 5);\n\t\t\t\t\t_isV8Plus && subProp === \"line\" && (subProp = \"stroke\"); // in v8, lineColor became strokeColor.\n\t\t\t\t\tthis._pt = new PropTween(this._pt, target, p, 0, 0, _renderDirtyCache, {g: target.geometry || target});\n\t\t\t\t\ti = data.length;\n\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\t_addColorTween(_isV4 ? data[i] : data[i][subProp + \"Style\"], _isV4 ? p : \"color\", value, this);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t_addColorTween(target, p, value, this);\n\t\t\t\t}\n\t\t\t} else if (p === \"autoAlpha\") {\n\t\t\t\tthis._pt = new PropTween(this._pt, target, \"visible\", 0, 0, _renderAutoAlpha);\n\t\t\t\tthis.add(target, \"alpha\", target.alpha, value);\n\t\t\t\tthis._props.push(\"alpha\", \"visible\");\n\t\t\t} else if (p !== \"resolution\") {\n\t\t\t\tthis.add(target, p, \"get\", value);\n\t\t\t}\n\t\t\tthis._props.push(p);\n\t\t}\n\t}\n};\n\n_getGSAP() && gsap.registerPlugin(PixiPlugin);\n\nexport { PixiPlugin as default };"], "names": ["_windowExists", "window", "_getGSAP", "gsap", "registerPlugin", "_isFunction", "value", "_warn", "message", "console", "warn", "_filterClass", "name", "_PIXI", "filters", "_applyMatrix", "m", "m2", "y", "x", "temp", "i", "z", "_setSaturation", "n", "inv", "r", "_lumR", "g", "_lumG", "b", "_lumB", "_colorize", "color", "amount", "c", "_splitColor", "_setHue", "Math", "PI", "cos", "s", "sin", "_setContrast", "_getFilter", "target", "type", "filter", "filterClass", "length", "blur", "push", "_addColorMatrixFilterCacheTween", "p", "plugin", "cache", "vars", "add", "_props", "_applyBrightnessToMatrix", "brightness", "matrix", "_parseColorMatrixFilter", "v", "pg", "startMatrix", "_gsColorMatrixFilter", "_copy", "obj", "copy", "_CMFdefaults", "combine", "combineCMF", "colorMatrixFilter", "resolution", "contrast", "hue", "colorizeAmount", "saturation", "_idMatrix", "slice", "colorize", "_renderColor", "ratio", "t", "set", "_renderDirtyCache", "_isV8Plus", "fill", "stroke", "dirty", "clearDirty", "_renderAutoAlpha", "data", "visible", "alpha", "_addColorTween", "currentValue", "startColor", "indexOf", "substr", "endColor", "_pt", "PropTween", "_getSetter", "_isString", "_degreesToRadians", "char<PERSON>t", "parseFloat", "_DEG2RAD", "_renderPropWithEnd", "e", "round", "_addRotationalPropTween", "property", "startNum", "endValue", "radians", "direction", "pt", "cap", "isString", "relative", "endNum", "change", "finalValue", "split", "_initCore", "_coreInitted", "version", "PIXI", "VERSION", "_isV4", "utils", "splitColor", "_colorProps", "tint", "lineColor", "fillColor", "strokeColor", "_xyContexts", "_contexts", "tileX", "tileY", "_colorMatrixFilterProps", "PixiPlugin", "register", "core", "Plugin", "propTween", "getSetter", "headless", "registerPIXI", "pixi", "init", "values", "context", "axis", "colorMatrix", "padding", "subProp", "toLowerCase", "this", "blurPadding", "max", "Graphics", "geometry", "graphicsData"], "mappings": ";;;;;;;;;6MAYiB,SAAhBA,UAAyC,oBAAZC,OAClB,SAAXC,WAAiBC,GAASH,MAAoBG,EAAOF,OAAOE,OAASA,EAAKC,gBAAkBD,EAC9E,SAAdE,EAAcC,SAA2B,mBAAXA,EACtB,SAARC,EAAQC,UAAWC,QAAQC,KAAKF,GAKjB,SAAfG,EAAeC,UAAQP,EAAYQ,EAAMD,IAASC,EAAMD,GAAQC,EAAMC,QAAQF,GAC/D,SAAfG,EAAgBC,EAAGC,OAIjBC,EAAGC,EAHAC,EAAO,GACVC,EAAI,EACJC,EAAI,MAEAJ,EAAI,EAAGA,EAAI,EAAGA,IAAK,KAClBC,EAAI,EAAGA,EAAI,EAAGA,IAClBG,EAAW,IAANH,EAAWH,EAAEK,EAAI,GAAK,EAC3BD,EAAKC,EAAIF,GAAKH,EAAEK,GAAOJ,EAAGE,GAAKH,EAAEK,EAAE,GAAKJ,EAAGE,EAAI,GAAKH,EAAEK,EAAE,GAAKJ,EAAGE,EAAI,IAAMH,EAAEK,EAAE,GAAKJ,EAAGE,EAAI,IAAMG,EAEjGD,GAAK,SAECD,EAES,SAAjBG,EAAkBP,EAAGQ,OAChBC,EAAM,EAAID,EACbE,EAAID,EAAME,EACVC,EAAIH,EAAMI,EACVC,EAAIL,EAAMM,SACJhB,EAAa,CAACW,EAAIF,EAAGI,EAAGE,EAAG,EAAG,EAAGJ,EAAGE,EAAIJ,EAAGM,EAAG,EAAG,EAAGJ,EAAGE,EAAGE,EAAIN,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAIR,GAEnF,SAAZgB,EAAahB,EAAGiB,EAAOC,OAClBC,EAAIC,EAAYH,GACnBP,EAAIS,EAAE,GAAK,IACXP,EAAIO,EAAE,GAAK,IACXL,EAAIK,EAAE,GAAK,IACXV,EAAM,EAAIS,SACJnB,EAAa,CAACU,EAAMS,EAASR,EAAIC,EAAOO,EAASR,EAAIG,EAAOK,EAASR,EAAIK,EAAO,EAAG,EAAGG,EAASN,EAAID,EAAOF,EAAMS,EAASN,EAAIC,EAAOK,EAASN,EAAIG,EAAO,EAAG,EAAGG,EAASJ,EAAIH,EAAOO,EAASJ,EAAID,EAAOJ,EAAMS,EAASJ,EAAIC,EAAO,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAIf,GAEpP,SAAVqB,EAAWrB,EAAGQ,GACbA,GAAKc,KAAKC,GAAK,QACXJ,EAAIG,KAAKE,IAAIhB,GAChBiB,EAAIH,KAAKI,IAAIlB,UACPT,EAAa,CAAEY,EAASQ,GAAK,EAAIR,GAAYc,GAAMd,EAAUE,EAASM,GAAMN,EAAYY,GAAMZ,EAAUE,EAASI,GAAMJ,EAAYU,GAAK,EAAIV,GAAS,EAAG,EAAIJ,EAASQ,GAAMR,EAAgB,KAAJc,EAAaZ,EAASM,GAAK,EAAIN,GAAgB,IAAJY,EAAYV,EAASI,GAAMJ,GAAiB,KAALU,EAAa,EAAG,EAAId,EAASQ,GAAMR,EAAYc,IAAO,EAAId,GAAWE,EAASM,GAAMN,EAAYY,EAAIZ,EAASE,EAASI,GAAK,EAAIJ,GAAYU,EAAIV,EAAQ,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAIf,GAE9b,SAAf2B,EAAgB3B,EAAGQ,UAAMT,EAAa,CAACS,EAAE,EAAE,EAAE,EAAE,IAAO,EAAIA,GAAI,EAAEA,EAAE,EAAE,EAAE,IAAO,EAAIA,GAAI,EAAE,EAAEA,EAAE,EAAE,IAAO,EAAIA,GAAI,EAAE,EAAE,EAAE,EAAE,GAAIR,GAC3G,SAAb4B,EAAcC,EAAQC,OAIpBC,EAHGC,EAAcrC,EAAamC,GAC9BhC,EAAU+B,EAAO/B,SAAW,GAC5BO,EAAIP,EAAQmC,WAEbD,GAAezC,EAAMuC,EAAO,8CACd,IAALzB,MACJP,EAAQO,aAAc2B,SAClBlC,EAAQO,UAGjB0B,EAAS,IAAIC,EACA,eAATF,IACHC,EAAOG,KAAO,GAEfpC,EAAQqC,KAAKJ,GACbF,EAAO/B,QAAUA,EACViC,EAE0B,SAAlCK,EAAmCC,EAAGC,EAAQC,EAAOC,GACpDF,EAAOG,IAAIF,EAAOF,EAAGE,EAAMF,GAAIG,EAAKH,IACpCC,EAAOI,OAAOP,KAAKE,GAEO,SAA3BM,EAA4BC,EAAYC,OAEtCzC,EAAO,IADUT,EAAa,6BAE/BS,EAAKyC,OAASA,EACdzC,EAAKwC,WAAWA,GAAY,GACrBxC,EAAKyC,OAWa,SAA1BC,EAA2BjB,EAAQkB,EAAGC,OAIpC3C,EAAGwC,EAAQI,EAHRlB,EAASH,EAAWC,EAAQ,qBAC/BU,EAAQV,EAAOqB,qBAAuBrB,EAAOqB,sBAXvC,SAARC,MAAQC,OAENf,EADGgB,EAAO,OAENhB,KAAKe,EACTC,EAAKhB,GAAKe,EAAIf,UAERgB,EAK+DF,CAAMG,GAC3EC,EAAUR,EAAES,cAAgB,sBAAuBT,IAAMA,EAAEU,mBAE5DR,EAAclB,EAAOc,OACjBE,EAAEW,aACL3B,EAAO2B,WAAaX,EAAEW,YAEnBX,EAAEF,QAAUE,EAAEF,OAAOZ,SAAWgB,EAAYhB,QAC/CY,EAASE,EAAEF,OACY,IAAnBN,EAAMoB,UACTvB,EAAgC,WAAYY,EAAIT,EAAOe,GAEpDf,EAAMqB,KACTxB,EAAgC,MAAOY,EAAIT,EAAOe,GAE1B,IAArBf,EAAMK,YACTR,EAAgC,aAAcY,EAAIT,EAAOe,GAEtDf,EAAMsB,iBACTzB,EAAgC,WAAYY,EAAIT,EAAOe,GACvDlB,EAAgC,iBAAkBY,EAAIT,EAAOe,IAErC,IAArBf,EAAMuB,YACT1B,EAAgC,aAAcY,EAAIT,EAAOe,KAI1DT,EAASkB,EAAUC,QACD,MAAdjB,EAAEY,UACLd,EAASlB,EAAakB,GAASE,EAAEY,UACjCvB,EAAgC,WAAYY,EAAIT,EAAOQ,IAC1B,IAAnBR,EAAMoB,WACZJ,EACHV,EAASlB,EAAakB,EAAQN,EAAMoB,UAEpCvB,EAAgC,WAAYY,EAAIT,EAAOe,IAG5C,MAATP,EAAEa,KACLf,EAASxB,EAAQwB,GAASE,EAAEa,KAC5BxB,EAAgC,MAAOY,EAAIT,EAAOQ,IACxCR,EAAMqB,MACZL,EACHV,EAASxB,EAAQwB,EAAQN,EAAMqB,KAE/BxB,EAAgC,MAAOY,EAAIT,EAAOe,IAGhC,MAAhBP,EAAEH,YACLC,EAASF,GAA0BI,EAAEH,WAAYC,GACjDT,EAAgC,aAAcY,EAAIT,EAAOQ,IAC1B,IAArBR,EAAMK,aACZW,EACHV,EAASF,EAAyBJ,EAAMK,WAAYC,GAEpDT,EAAgC,aAAcY,EAAIT,EAAOe,IAGzC,MAAdP,EAAEkB,UACLlB,EAAEc,eAAkB,mBAAoBd,GAAMA,EAAEc,eAAiB,EACjEhB,EAAS7B,EAAU6B,EAAQE,EAAEkB,SAAUlB,EAAEc,gBACzCzB,EAAgC,WAAYY,EAAIT,EAAOQ,GACvDX,EAAgC,iBAAkBY,EAAIT,EAAOQ,IACnDR,EAAMsB,iBACZN,EACHV,EAAS7B,EAAU6B,EAAQN,EAAM0B,SAAU1B,EAAMsB,iBAEjDzB,EAAgC,WAAYY,EAAIT,EAAOe,GACvDlB,EAAgC,iBAAkBY,EAAIT,EAAOe,KAG3C,MAAhBP,EAAEe,YACLjB,EAAStC,EAAesC,GAASE,EAAEe,YACnC1B,EAAgC,aAAcY,EAAIT,EAAOQ,IAC1B,IAArBR,EAAMuB,aACZP,EACHV,EAAStC,EAAesC,EAAQN,EAAMuB,YAEtC1B,EAAgC,aAAcY,EAAIT,EAAOe,KAI5DjD,EAAIwC,EAAOZ,aACG,IAAL5B,GACJwC,EAAOxC,KAAO4C,EAAY5C,IAC7B2C,EAAGP,IAAIQ,EAAa5C,EAAG4C,EAAY5C,GAAIwC,EAAOxC,GAAI,qBAGpD2C,EAAGN,OAAOP,KAAK,qBAED,SAAf+B,EAAgBC,SAAQC,IAAAA,EAAG/B,IAAAA,EAAGpB,IAAAA,OAC7BoD,IADoCA,KAChCD,EAAG/B,EAAGpB,EAAM,IAAM,GAAKA,EAAM,IAAM,EAAIA,EAAM,IAE9B,SAApBqD,EAAqBH,SAAQvD,IAAAA,EACxB2D,GACH3D,EAAE4D,OACF5D,EAAE6D,UACQ7D,IACVA,EAAE8D,QACF9D,EAAE+D,cAGe,SAAnBC,EAAoBT,EAAOU,GAC1BA,EAAKT,EAAEU,UAAYD,EAAKT,EAAEW,MAEV,SAAjBC,EAAkBnD,EAAQQ,EAAG/C,EAAOgD,OAC/B2C,EAAepD,EAAOQ,GACzB6C,EAAa9D,EAAY/B,EAAY4F,GAAgBpD,EAAUQ,EAAE8C,QAAQ,SAAW9F,EAAYwC,EAAO,MAAQQ,EAAE+C,OAAO,KAAQ/C,EAAI,MAAQA,EAAE+C,OAAO,MAAUH,GAC/JI,EAAWjE,EAAY9B,GACxBgD,EAAOgD,IAAM,IAAIC,EAAUjD,EAAOgD,IAAKzD,EAAQQ,EAAG,EAAG,EAAG6B,EAAc,CAACE,EAAEvC,EAAQQ,EAAEA,EAAGpB,MAAMiE,EAAYb,IAAImB,EAAW3D,EAAQQ,KAC/HC,EAAOG,IAAIyC,EAAY,EAAGA,EAAW,GAAIG,EAAS,IAClD/C,EAAOG,IAAIyC,EAAY,EAAGA,EAAW,GAAIG,EAAS,IAClD/C,EAAOG,IAAIyC,EAAY,EAAGA,EAAW,GAAIG,EAAS,IAQvC,SAAZI,EAAYnG,SAA2B,iBAAXA,EACR,SAApBoG,EAAoBpG,UAAUmG,EAAUnG,IAA8B,MAApBA,EAAMqG,OAAO,GAAcrG,EAAM8F,OAAO,EAAG,GAAMQ,WAAWtG,EAAM8F,OAAO,IAAMS,EAAYvG,EAAQuG,EAChI,SAArBC,EAAsB3B,EAAOU,UAASA,EAAKR,IAAIQ,EAAKT,EAAGS,EAAKxC,EAAa,IAAV8B,EAAcU,EAAKkB,EAAKzE,KAAK0E,MAAkC,KAA3BnB,EAAKpD,EAAIoD,EAAK1D,EAAIgD,IAAmB,IAASU,GACvH,SAA1BoB,EAA2B3D,EAAQT,EAAQqE,EAAUC,EAAUC,EAAUC,OAOvEC,EAAWC,EANRC,EAAM,KAAOH,EAAUR,EAAW,GACrCY,EAAWhB,EAAUW,GACrBM,EAAYD,GAAmC,MAAvBL,EAAST,OAAO,KAAgBS,EAAST,OAAO,GAAK,KAAO,EACpFgB,EAASf,WAAWc,EAAWN,EAAShB,OAAO,GAAKgB,IAAaC,EAAUR,EAAW,GACtFe,EAASF,EAAWC,EAASD,EAAWC,EAASR,EACjDU,EAAaV,EAAWS,SAErBH,IAEe,WADlBH,EAAYF,EAASU,MAAM,KAAK,MAE/BF,GAAUJ,KACKI,GAAUJ,EAAM,KAC9BI,GAAWA,EAAS,EAAKJ,GAAOA,GAGhB,OAAdF,GAAsBM,EAAS,EAClCA,GAAWA,EAAe,KAANJ,GAAcA,KAAUI,EAASJ,GAAOA,EACpC,QAAdF,GAAgC,EAATM,IACjCA,GAAWA,EAAe,KAANJ,GAAcA,KAAUI,EAASJ,GAAOA,IAG9DlE,EAAOgD,IAAMiB,EAAK,IAAIhB,EAAUjD,EAAOgD,IAAKzD,EAAQqE,EAAUC,EAAUS,EAAQd,GAChFS,EAAGR,EAAIc,EACAN,EAEI,SAAZQ,QACMC,EAAc,CAClB7H,EAAOD,QAEH+H,GADJpH,EAAQmH,EAAenH,GAAUb,KAAmBC,OAAOiI,OACnCrH,EAAMsH,SAAWvB,WAAW/F,EAAMsH,QAAQL,MAAM,KAAK,KAAQ,EACrFM,EAAoB,IAAZH,EACR1C,EAAuB,GAAX0C,EACZ7F,EAAc,qBAAAH,UAAS9B,EAAKkI,MAAMC,WAAwC,QAA5BrG,EAAQ,IAAImE,OAAO,EAAE,GAAc,IAAMnE,EAAMmE,OAAO,GAAKnE,KAnP5G,IAAI9B,EAAMiC,EAAa4F,EAAcnH,EAAO0F,EAAWC,EAAY4B,EAAO7C,EAqPtElE,EAAGgC,EAhPN0B,EAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GACnDpD,EAAQ,QACRE,EAAQ,OACRE,EAAQ,QA4ERuC,EAAe,CAACK,SAAS,EAAGG,WAAW,EAAGD,eAAe,EAAGI,SAAS,mBAAoBL,IAAI,EAAGhB,WAAW,GAsH3G2E,EAAc,CAACC,KAAK,EAAGC,UAAU,EAAGC,UAAU,EAAGC,YAAY,GAC7DC,EAAc,0DAA0Dd,MAAM,KAC9Ee,EAAY,CAAC1H,EAAE,WAAYD,EAAE,WAAY4H,MAAM,eAAgBC,MAAM,gBACrEC,EAA0B,CAACvE,kBAAkB,EAAGK,WAAW,EAAGH,SAAS,EAAGC,IAAI,EAAGK,SAAS,EAAGJ,eAAe,EAAGjB,WAAW,EAAGY,WAAW,GACxIqC,EAAWvE,KAAKC,GAAK,IA0CtB,IAAKlB,EAAI,EAAGA,EAAIuH,EAAY3F,OAAQ5B,IACnCgC,EAAIuF,EAAYvH,GAChBwH,EAAUxF,EAAI,KAAOA,EACrBwF,EAAUxF,EAAI,KAAOA,MAIT4F,EAAa,CACzBhB,QAAS,SACTrH,KAAM,OACNsI,2BAASC,EAAMC,EAAQC,GACtBlJ,EAAOgJ,EACP5C,EAAY8C,EACZ7C,EAAa4C,EAAOE,UACpBvB,KAEDwB,UAAU,EACVC,mCAAaC,GACZ5I,EAAQ4I,GAETC,mBAAK7G,EAAQ8G,MACZ9I,GAASkH,KACJlH,SACJN,EAAM,uDACC,MAEJqJ,EAASC,EAAMvJ,EAAOwJ,EAAa/G,EAAQM,EAAG0G,EAAS1I,EAAGwE,EAAMmE,MAC/D3G,KAAKsG,EAAQ,IACjBC,EAAUf,EAAUxF,GACpB/C,EAAQqJ,EAAOtG,GACXuG,EACHC,GAAQxG,EAAEsD,OAAOtD,EAAEJ,OAAO,GAAGgH,cAAc9D,QAAQ,KAAO,IAAM,SAC3D1C,IAAIZ,EAAO+G,GAAUC,EAAMhH,EAAO+G,GAASC,GAAoB,SAAZD,EAAsBlD,EAAkBpG,GAASA,EAAO,EAAG,EAAG,EAAG,EAAG,EAAG,QACzH,GAAU,UAAN+C,GAAuB,WAANA,GAAwB,UAANA,GAAuB,cAANA,OACzDI,IAAIZ,EAAOQ,GAAI,IAAKR,EAAOQ,GAAGlC,EAAGb,QACjCmD,IAAIZ,EAAOQ,GAAI,IAAKR,EAAOQ,GAAGnC,EAAGZ,QAChC,GAAU,aAAN+C,GAA0B,UAANA,EAC9B4D,EAAwBiD,KAAMrH,EAAQQ,EAAGR,EAAOQ,GAAI/C,EAAa,aAAN+C,QACrD,GAAI2F,EAAwB3F,GAC7ByG,IACJhG,EAAwBjB,EAAQ8G,EAAOlF,mBAAqBkF,EAAQO,MACpEJ,GAAc,QAET,GAAU,SAANzG,GAAsB,UAANA,GAAuB,UAANA,GAAuB,gBAANA,MAC5DN,EAASH,EAAWC,EAAQ,mBACvBY,IAAIV,EAAQM,EAAGN,EAAOM,GAAI/C,GACJ,IAAvBqJ,EAAOQ,gBACVJ,EAAUJ,EAAOQ,aAA4C,EAA7B7H,KAAK8H,IAAIrH,EAAOM,GAAI/C,GACpDe,EAAIwB,EAAO/B,QAAQmC,QACL,IAAL5B,GACRwB,EAAO/B,QAAQO,GAAG0I,QAAUzH,KAAK8H,IAAIvH,EAAO/B,QAAQO,GAAG0I,QAASA,QAG5D,GAAIxB,EAAYlF,OACX,cAANA,GAA2B,cAANA,GAA2B,gBAANA,IAAwBR,aAAkBhC,EAAMwJ,SAAU,CACxGxE,EAAO,cAAehD,EAAS,CAACA,IAAWA,EAAOyH,UAAYzH,GAAQ0H,aACtEP,EAAU3G,EAAE+C,OAAO,EAAG/C,EAAEJ,OAAS,GACjCsC,GAAyB,SAAZyE,IAAuBA,EAAU,eACzC1D,IAAM,IAAIC,EAAU2D,KAAK5D,IAAKzD,EAAQQ,EAAG,EAAG,EAAGiC,EAAmB,CAAC1D,EAAGiB,EAAOyH,UAAYzH,IAC9FxB,EAAIwE,EAAK5C,aACK,IAAL5B,GACR2E,EAAeoC,EAAQvC,EAAKxE,GAAKwE,EAAKxE,GAAG2I,EAAU,SAAU5B,EAAQ/E,EAAI,QAAS/C,EAAO4J,WAG1FlE,EAAenD,EAAQQ,EAAG/C,EAAO4J,UAElB,cAAN7G,QACLiD,IAAM,IAAIC,EAAU2D,KAAK5D,IAAKzD,EAAQ,UAAW,EAAG,EAAG+C,QACvDnC,IAAIZ,EAAQ,QAASA,EAAOkD,MAAOzF,QACnCoD,OAAOP,KAAK,QAAS,YACV,eAANE,QACLI,IAAIZ,EAAQQ,EAAG,MAAO/C,QAEvBoD,OAAOP,KAAKE,MAKpBnD,KAAcC,EAAKC,eAAe6I"}